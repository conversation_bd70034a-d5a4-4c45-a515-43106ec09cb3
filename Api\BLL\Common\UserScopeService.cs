using Common.Autofac;
using Common.Exceptions;
using DAL.SysDAL;
using DAL.VideoDAL;
using Entity.Entitys.SysEntity;
using Entity.Entitys.VideoEntity;
using Microsoft.EntityFrameworkCore;

namespace BLL.Common
{
    /// <summary>
    /// 用户范围查询服务
    /// 根据用户类型获取相应的用户范围，支持不同层级的查询
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class UserScopeService(SysUserDAL sysUserDAL, UserDAL userDAL)
    {
        private readonly SysUserDAL _sysUserDAL = sysUserDAL;
        private readonly UserDAL _userDAL = userDAL;

        #region 获取用户ID范围

        /// <summary>
        /// 根据当前用户获取可查询的所有用户ID列表
        /// </summary>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>用户ID列表</returns>
        public async Task<List<string>> GetAccessibleUserIdsAsync(string currentUserId)
        {
            var currentUser = await GetCurrentUserAsync(currentUserId);
            return await GetAccessibleUserIdsByUserTypeAsync(currentUser.UserType, currentUserId);
        }

        /// <summary>
        /// 根据指定用户获取可查询的所有用户ID列表
        /// </summary>
        /// <param name="targetUserId">目标用户ID</param>
        /// <returns>用户ID列表</returns>
        public async Task<List<int>> GetAccessibleUserIdsByTargetUserAsync(string targetUserId)
        {
            var targetUser = await GetCurrentUserAsync(targetUserId);
            return await GetAccessibleUserIdsByUserTypeAsync(targetUser.UserType, targetUserId);
        }

        /// <summary>
        /// 根据用户类型获取可查询的所有用户ID列表
        /// </summary>
        /// <param name="userType">用户类型：1-超级管理员，2-管理员，3-员工</param>
        /// <param name="userId">用户ID</param>
        /// <returns>用户ID列表</returns>
        public async Task<List<int>> GetAccessibleUserIdsByUserTypeAsync(byte userType, string userId)
        {
            return userType switch
            {
                1 => await GetAllUserIdsAsync(), // 超级管理员：所有用户
                2 => await GetAdminAccessibleUserIdsAsync(userId), // 管理员：下属员工的所有用户
                3 => await GetEmployeeAccessibleUserIdsAsync(userId), // 员工：自己的用户
                _ => throw new BusinessException("无效的用户类型")
            };
        }

        #endregion

        #region 获取员工ID范围

        /// <summary>
        /// 根据当前用户获取可查询的员工ID列表（只返回一层）
        /// </summary>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>员工ID列表</returns>
        public async Task<List<string>> GetAccessibleEmployeeIdsAsync(string currentUserId)
        {
            var currentUser = await GetCurrentUserAsync(currentUserId);
            return await GetAccessibleEmployeeIdsByUserTypeAsync(currentUser.UserType, currentUserId);
        }

        /// <summary>
        /// 根据指定用户获取可查询的员工ID列表（只返回一层）
        /// </summary>
        /// <param name="targetUserId">目标用户ID</param>
        /// <returns>员工ID列表</returns>
        public async Task<List<string>> GetAccessibleEmployeeIdsByTargetUserAsync(string targetUserId)
        {
            var targetUser = await GetCurrentUserAsync(targetUserId);
            return await GetAccessibleEmployeeIdsByUserTypeAsync(targetUser.UserType, targetUserId);
        }

        /// <summary>
        /// 根据用户类型获取可查询的员工ID列表（只返回一层）
        /// </summary>
        /// <param name="userType">用户类型：1-超级管理员，2-管理员，3-员工</param>
        /// <param name="userId">用户ID</param>
        /// <returns>员工ID列表</returns>
        public async Task<List<string>> GetAccessibleEmployeeIdsByUserTypeAsync(byte userType, string userId)
        {
            return userType switch
            {
                1 => await GetAllEmployeeIdsAsync(), // 超级管理员：所有员工
                2 => await GetAdminDirectEmployeeIdsAsync(userId), // 管理员：直属员工
                3 => [userId], // 员工：只有自己
                _ => throw new BusinessException("无效的用户类型")
            };
        }

        #endregion

        #region 获取管理员ID范围

        /// <summary>
        /// 根据当前用户获取可查询的管理员ID列表
        /// </summary>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>管理员ID列表</returns>
        public async Task<List<string>> GetAccessibleAdminIdsAsync(string currentUserId)
        {
            var currentUser = await GetCurrentUserAsync(currentUserId);
            return await GetAccessibleAdminIdsByUserTypeAsync(currentUser.UserType, currentUserId);
        }

        /// <summary>
        /// 根据用户类型获取可查询的管理员ID列表
        /// </summary>
        /// <param name="userType">用户类型：1-超级管理员，2-管理员，3-员工</param>
        /// <param name="userId">用户ID</param>
        /// <returns>管理员ID列表</returns>
        public async Task<List<string>> GetAccessibleAdminIdsByUserTypeAsync(byte userType, string userId)
        {
            return userType switch
            {
                1 => await GetAllAdminIdsAsync(), // 超级管理员：所有管理员
                2 => [userId], // 管理员：只有自己
                3 => [], // 员工：无权限查看管理员
                _ => throw new BusinessException("无效的用户类型")
            };
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        private async Task<SysUser> GetCurrentUserAsync(string userId)
        {
            return await _sysUserDAL.GetByIdAsync(userId)
                ?? throw new BusinessException("用户不存在");
        }

        /// <summary>
        /// 获取所有用户ID
        /// </summary>
        /// <returns>所有用户ID列表</returns>
        private async Task<List<int>> GetAllUserIdsAsync()
        {
            return await _userDAL.GetQueryable()
                .Select(u => u.Id)
                .ToListAsync();
        }

        /// <summary>
        /// 获取管理员可访问的用户ID（管理员下属员工的所有用户）
        /// </summary>
        /// <param name="adminUserId">管理员用户ID</param>
        /// <returns>用户ID列表</returns>
        private async Task<List<int>> GetAdminAccessibleUserIdsAsync(string adminUserId)
        {
            // 获取管理员下属的所有员工ID
            var employeeIds = await GetAdminAllEmployeeIdsAsync(adminUserId);

            if (!employeeIds.Any())
                return [];

            // 获取这些员工绑定的所有用户ID
            return await _userDAL.GetQueryable()
                .Where(u => u.EmployeeId != null && employeeIds.Contains(u.EmployeeId))
                .Select(u => u.Id)
                .ToListAsync();
        }

        /// <summary>
        /// 获取员工可访问的用户ID（员工自己的用户）
        /// </summary>
        /// <param name="employeeUserId">员工用户ID</param>
        /// <returns>用户ID列表</returns>
        private async Task<List<int>> GetEmployeeAccessibleUserIdsAsync(string employeeUserId)
        {
            return await _userDAL.GetQueryable()
                .Where(u => u.EmployeeId == employeeUserId)
                .Select(u => u.Id)
                .ToListAsync();
        }

        /// <summary>
        /// 获取所有员工ID
        /// </summary>
        /// <returns>所有员工ID列表</returns>
        private async Task<List<string>> GetAllEmployeeIdsAsync()
        {
            return await _sysUserDAL.GetQueryable()
                .Where(u => u.UserType == 3) // 员工类型
                .Select(u => u.UserId)
                .ToListAsync();
        }

        /// <summary>
        /// 获取管理员的直属员工ID
        /// </summary>
        /// <param name="adminUserId">管理员用户ID</param>
        /// <returns>直属员工ID列表</returns>
        private async Task<List<string>> GetAdminDirectEmployeeIdsAsync(string adminUserId)
        {
            return await _sysUserDAL.GetQueryable()
                .Where(u => u.UserType == 3 && u.ParentUserId == adminUserId)
                .Select(u => u.UserId)
                .ToListAsync();
        }

        /// <summary>
        /// 获取管理员下属的所有员工ID（包括间接下属）
        /// </summary>
        /// <param name="adminUserId">管理员用户ID</param>
        /// <returns>所有下属员工ID列表</returns>
        private async Task<List<string>> GetAdminAllEmployeeIdsAsync(string adminUserId)
        {
            // 目前系统只有三层：超管-管理员-员工，所以管理员的下属就是直属员工
            return await GetAdminDirectEmployeeIdsAsync(adminUserId);
        }

        /// <summary>
        /// 获取所有管理员ID
        /// </summary>
        /// <returns>所有管理员ID列表</returns>
        private async Task<List<string>> GetAllAdminIdsAsync()
        {
            return await _sysUserDAL.GetQueryable()
                .Where(u => u.UserType == 2) // 管理员类型
                .Select(u => u.UserId)
                .ToListAsync();
        }

        #endregion

        #region 权限验证方法

        /// <summary>
        /// 验证当前用户是否有权限访问指定用户
        /// </summary>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="targetUserId">目标用户ID</param>
        /// <returns>是否有权限</returns>
        public async Task<bool> CanAccessUserAsync(string currentUserId, int targetUserId)
        {
            var accessibleUserIds = await GetAccessibleUserIdsAsync(currentUserId);
            return accessibleUserIds.Contains(targetUserId);
        }

        /// <summary>
        /// 验证当前用户是否有权限访问指定员工
        /// </summary>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="targetEmployeeId">目标员工ID</param>
        /// <returns>是否有权限</returns>
        public async Task<bool> CanAccessEmployeeAsync(string currentUserId, string targetEmployeeId)
        {
            var accessibleEmployeeIds = await GetAccessibleEmployeeIdsAsync(currentUserId);
            return accessibleEmployeeIds.Contains(targetEmployeeId);
        }

        /// <summary>
        /// 验证当前用户是否有权限访问指定管理员
        /// </summary>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="targetAdminId">目标管理员ID</param>
        /// <returns>是否有权限</returns>
        public async Task<bool> CanAccessAdminAsync(string currentUserId, string targetAdminId)
        {
            var accessibleAdminIds = await GetAccessibleAdminIdsAsync(currentUserId);
            return accessibleAdminIds.Contains(targetAdminId);
        }

        #endregion

        #region 便捷查询方法

        /// <summary>
        /// 获取当前用户可访问的用户查询条件（用于EF查询）
        /// </summary>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>用户查询表达式</returns>
        public async Task<System.Linq.Expressions.Expression<Func<User, bool>>> GetUserAccessFilterAsync(string currentUserId)
        {
            var currentUser = await GetCurrentUserAsync(currentUserId);

            return currentUser.UserType switch
            {
                1 => u => true, // 超级管理员：所有用户
                2 => u => u.EmployeeId != null && _sysUserDAL.GetQueryable()
                    .Where(emp => emp.UserType == 3 && emp.ParentUserId == currentUserId)
                    .Select(emp => emp.UserId)
                    .Contains(u.EmployeeId), // 管理员：下属员工的用户
                3 => u => u.EmployeeId == currentUserId, // 员工：自己的用户
                _ => u => false
            };
        }

        /// <summary>
        /// 获取当前用户可访问的员工查询条件（用于EF查询）
        /// </summary>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>员工查询表达式</returns>
        public async Task<System.Linq.Expressions.Expression<Func<SysUser, bool>>> GetEmployeeAccessFilterAsync(string currentUserId)
        {
            var currentUser = await GetCurrentUserAsync(currentUserId);

            return currentUser.UserType switch
            {
                1 => emp => emp.UserType == 3, // 超级管理员：所有员工
                2 => emp => emp.UserType == 3 && emp.ParentUserId == currentUserId, // 管理员：直属员工
                3 => emp => emp.UserId == currentUserId, // 员工：只有自己
                _ => emp => false
            };
        }

        /// <summary>
        /// 根据用户类型描述获取用户类型枚举值
        /// </summary>
        /// <param name="userTypeDescription">用户类型描述</param>
        /// <returns>用户类型枚举值</returns>
        public static byte GetUserTypeByDescription(string userTypeDescription)
        {
            return userTypeDescription switch
            {
                "超级管理员" => 1,
                "管理员" => 2,
                "员工" => 3,
                _ => throw new BusinessException($"无效的用户类型描述：{userTypeDescription}")
            };
        }

        /// <summary>
        /// 根据用户类型枚举值获取用户类型描述
        /// </summary>
        /// <param name="userType">用户类型枚举值</param>
        /// <returns>用户类型描述</returns>
        public static string GetUserTypeDescription(byte userType)
        {
            return userType switch
            {
                1 => "超级管理员",
                2 => "管理员",
                3 => "员工",
                _ => "未知类型"
            };
        }

        /// <summary>
        /// 检查用户类型是否有效
        /// </summary>
        /// <param name="userType">用户类型</param>
        /// <returns>是否有效</returns>
        public static bool IsValidUserType(byte userType)
        {
            return userType is >= 1 and <= 3;
        }

        /// <summary>
        /// 检查用户是否为超级管理员
        /// </summary>
        /// <param name="userType">用户类型</param>
        /// <returns>是否为超级管理员</returns>
        public static bool IsSuperAdmin(byte userType) => userType == 1;

        /// <summary>
        /// 检查用户是否为管理员（包括超级管理员）
        /// </summary>
        /// <param name="userType">用户类型</param>
        /// <returns>是否为管理员</returns>
        public static bool IsAdmin(byte userType) => userType is 1 or 2;

        /// <summary>
        /// 检查用户是否为员工
        /// </summary>
        /// <param name="userType">用户类型</param>
        /// <returns>是否为员工</returns>
        public static bool IsEmployee(byte userType) => userType == 3;

        #endregion
    }
}
