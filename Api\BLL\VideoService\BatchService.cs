using BLL.SysService;
using Common.Autofac;
using Common.Caches;
using Common.Exceptions;
using Common.JWT;
using DAL.VideoDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Entity.Entitys.SysEntity;
using Microsoft.EntityFrameworkCore;

namespace BLL.VideoService
{
    /// <summary>
    /// 批次业务服务类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class BatchService(BatchDAL batchDAL, VideoDAL videoDAL, SysLogService logService,
        UserBatchRecordDAL userBatchRecordDAL, UserDAL userDAL)
    {
        private readonly BatchDAL _batchDAL = batchDAL;
        private readonly VideoDAL _videoDAL = videoDAL;
        private readonly SysLogService _logService = logService;
        private readonly UserBatchRecordDAL _userBatchRecordDAL = userBatchRecordDAL;
        private readonly UserDAL _userDAL = userDAL;

        /// <summary>
        /// 添加批次
        /// </summary>
        /// <param name="createDto">创建批次DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>批次ID</returns>
        public async Task<int> AddBatchAsync(BatchCreateDto createDto, CurrentUserInfoDto currentUserInfo)
        {
            // 验证视频是否存在
            var video = await _videoDAL.GetByIdAsync(createDto.VideoId) ?? throw new BusinessException("指定的视频不存在");

            // 验证时间范围
            if (createDto.StartTime >= createDto.EndTime)
                throw new BusinessException("开始时间必须早于结束时间");

            // 创建批次实体
            var batch = new Batch
            {
                Name = createDto.Name,
                Description = createDto.Description,
                VideoId = createDto.VideoId,
                VideoTitle = video.Title, // 冗余存储视频标题
                VideoDescription = video.Description,
                VideoCoverUrl = video.CoverUrl,
                VideoUrl = video.VideoUrl,
                VideoDuration = video.Duration,
                RewardAmount = video.RewardAmount,
                Questions = video.Questions,
                StartTime = createDto.StartTime,
                EndTime = createDto.EndTime,
                RedPacketAmount = createDto.RedPacketAmount,
                Status = 0, // 默认未开始
                CreateTime = DateTime.Now,
                CreatorId = int.TryParse(currentUserInfo.UserId, out var userId) ? userId : 0
            };

            // 添加批次
            await _batchDAL.AddAsync(batch);

            // 记录业务日志 - 使用Task.Run避免上下文冲突
            _ = Task.Run(async () =>
            {
                try
                {
                    await _logService.LogBusinessOperationAsync(new BusinessLogDto
                    {
                        Module = "批次管理",
                        Operation = "创建批次",
                        BusinessObject = "Batch",
                        ObjectId = batch.Id.ToString(),
                        DetailedInfo = $"创建批次：{batch.Name}",
                        AfterData = batch,
                        UserId = currentUserInfo.UserId,
                        Username = currentUserInfo.UserName,
                        Level = LogLevel.Information
                    });
                }
                catch
                {
                    // 忽略日志记录失败，不影响主业务
                }
            });

            return batch.Id;
        }

        /// <summary>
        /// 删除批次
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteBatchAsync(int batchId, CurrentUserInfoDto currentUserInfo)
        {
            // 获取批次信息
            var batch = await _batchDAL.GetByIdAsync(batchId)
                ?? throw new BusinessException("批次不存在");

            // 检查批次状态，进行中的批次不能删除
            if (batch.Status == 1)
                throw new BusinessException("进行中的批次不能删除");

            var result = await _batchDAL.DeleteAsync(batch);

            // 记录业务日志 - 使用Task.Run避免上下文冲突
            _ = Task.Run(async () =>
            {
                try
                {
                    await _logService.LogBusinessOperationAsync(new BusinessLogDto
                    {
                        Module = "批次管理",
                        Operation = "删除批次",
                        BusinessObject = "Batch",
                        ObjectId = batchId.ToString(),
                        DetailedInfo = $"删除批次：{batch.Name}",
                        BeforeData = batch,
                        UserId = currentUserInfo.UserId,
                        Username = currentUserInfo.UserName,
                        Level = LogLevel.Information
                    });
                }
                catch
                {
                    // 忽略日志记录失败，不影响主业务
                }
            });

            return result;
        }

        /// <summary>
        /// 获取批次详情
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <returns>批次响应DTO</returns>
        public async Task<BatchResponseDto?> GetBatchAsync(int batchId)
        {
            var batch = await _batchDAL.GetByIdAsync(batchId);
            if (batch == null) return null;

            return new BatchResponseDto
            {
                Id = batch.Id,
                Name = batch.Name,
                Description = batch.Description,
                VideoId = batch.VideoId,
                VideoTitle = batch.VideoTitle,
                VideoDescription = batch.VideoDescription,
                VideoCoverUrl = batch.VideoCoverUrl,
                VideoUrl = batch.VideoUrl,
                VideoDuration = batch.VideoDuration,
                RewardAmount = batch.RewardAmount,
                Questions = string.IsNullOrEmpty(batch.Questions) ? null :
                    JsonSerializer.Deserialize<List<VideoQuestionDto>>(batch.Questions, MemoryCacheHelper.DefaultJsonOptions),
                StartTime = batch.StartTime,
                EndTime = batch.EndTime,
                CurrentParticipants = batch.CurrentParticipants,
                RedPacketAmount = batch.RedPacketAmount,
                Status = batch.Status,
                CreateTime = batch.CreateTime,
                CreatorId = batch.CreatorId
            };
        }

        /// <summary>
        /// 分页查询批次列表
        /// </summary>
        /// <param name="queryDto">查询条件DTO</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<BatchResponseDto>> GetBatchPagedListAsync(BatchQueryDto queryDto)
        {
            var queryable = new BatchDAL.Queryable
            {
                Name = queryDto.Name,
                Status = queryDto.Status,
                StartTime = queryDto.StartTime,
                EndTime = queryDto.EndTime,
                PageIndex = queryDto.PageIndex,
                PageSize = queryDto.PageSize
            };

            var result = await _batchDAL.GetPagedListAsync(queryable);

            var responseList = (result.Items ?? []).Select(batch => new BatchResponseDto
            {
                Id = batch.Id,
                Name = batch.Name,
                Description = batch.Description,
                VideoId = batch.VideoId,
                VideoTitle = batch.VideoTitle,
                VideoDescription = batch.VideoDescription,
                VideoCoverUrl = batch.VideoCoverUrl,
                VideoUrl = batch.VideoUrl,
                VideoDuration = batch.VideoDuration,
                RewardAmount = batch.RewardAmount,
                Questions = string.IsNullOrEmpty(batch.Questions) ? null :
                    JsonSerializer.Deserialize<List<VideoQuestionDto>>(batch.Questions, MemoryCacheHelper.DefaultJsonOptions),
                StartTime = batch.StartTime,
                EndTime = batch.EndTime,
                CurrentParticipants = batch.CurrentParticipants,
                RedPacketAmount = batch.RedPacketAmount,
                Status = batch.Status,
                CreateTime = batch.CreateTime,
                CreatorId = batch.CreatorId
            }).ToList();

            return new PagedResult<BatchResponseDto>
            {
                Items = responseList,
                TotalCount = result.TotalCount,
                PageIndex = result.PageIndex,
                PageSize = result.PageSize
            };
        }

        /// <summary>
        /// 获取批次统计信息（支持权限控制）
        /// 使用新的UserBatchRecord表进行统计
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="currentJwtUserInfo">当前JWT用户信息</param>
        /// <returns>批次统计信息</returns>
        public async Task<BatchStatisticsDto> GetBatchStatisticsAsync(int batchId, UserInfo currentJwtUserInfo)
        {
            // 验证批次是否存在
            var batch = await _batchDAL.GetByIdAsync(batchId) ?? throw new BusinessException("指定的批次不存在");

            // 根据用户权限获取可访问的用户ID列表
            var accessibleUserIds = await GetAccessibleUserIdsAsync(currentJwtUserInfo);

            // 使用新的UserBatchRecord获取统计数据
            var statistics = await _userBatchRecordDAL.GetBatchStatisticsAsync(batchId, accessibleUserIds);

            // 组装统计结果
            return new BatchStatisticsDto
            {
                ViewCount = statistics.ViewerCount,
                CompleteViewCount = statistics.CompletedViewerCount,
                CompleteRate = statistics.TotalParticipants > 0 ?
                    Math.Round((decimal)statistics.CompletedViewerCount / statistics.TotalParticipants * 100, 2) : 0,

                TotalAnswerCount = statistics.AnswerCount,
                CorrectAnswerCount = (int)(statistics.AnswerCount * statistics.AverageCorrectRate / 100),
                CorrectRate = statistics.AverageCorrectRate,

                RewardCount = statistics.SuccessRewardCount,
                RewardAmount = statistics.TotalRewardAmount,

                TotalParticipants = statistics.TotalParticipants,
                NewUserCount = statistics.TotalParticipants, // 暂时等同于总参与人数

                // 对于单个批次，这些字段设为固定值
                TotalCount = 1,
                ActiveCount = batch.Status == 1 ? 1 : 0,
                CompletedCount = DateTime.Now > batch.EndTime ? 1 : 0,
                TotalRedPacketAmount = batch.RedPacketAmount
            };
        }

        /// <summary>
        /// 根据用户权限获取可访问的用户ID列表
        /// </summary>
        /// <param name="currentJwtUserInfo">当前JWT用户信息</param>
        /// <returns>可访问的用户ID列表，null表示不限制</returns>
        private async Task<List<int>?> GetAccessibleUserIdsAsync(UserInfo currentJwtUserInfo)
        {
            switch (currentJwtUserInfo.UserType)
            {
                case 1: // 超级管理员 - 可以查看所有用户
                    return null; // 返回null表示不限制用户ID

                case 2: // 管理员 - 可以查看所有员工及其用户
                    var allEmployeeUsers = await GetAllEmployeeUsersAsync();
                    return allEmployeeUsers.Select(u => u.Id).ToList();

                case 3: // 员工 - 只能查看自己绑定用户
                    var myUsers = await _userDAL.GetByEmployeeIdAsync(currentJwtUserInfo.UserId);
                    return myUsers.Select(u => u.Id).ToList();

                default:
                    throw new BusinessException("无效的用户类型");
            }
        }

        /// <summary>
        /// 获取所有员工的用户
        /// </summary>
        /// <returns>所有员工的用户列表</returns>
        private async Task<List<Entity.Entitys.VideoEntity.User>> GetAllEmployeeUsersAsync()
        {
            return await _userDAL.GetQueryable()
                .Where(u => !string.IsNullOrEmpty(u.EmployeeId))
                .ToListAsync();
        }
    }
}
