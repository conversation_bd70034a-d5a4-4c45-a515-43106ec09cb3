using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.VideoEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.VideoDAL
{
    /// <summary>
    /// 用户数据访问层实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class UserDAL(MyContext context) : BaseQueryDLL<User, UserDAL.Queryable>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 用户查询条件模型类
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 微信昵称(模糊查询)
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Nickname { get; set; }

            /// <summary>
            /// 绑定的员工ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? EmployeeId { get; set; }

            /// <summary>
            /// 开始时间
            /// </summary>
            [Query(QueryOperator.日期范围, relatedProperty: nameof(EndTime))]
            public DateTime? StartTime { get; set; }

            /// <summary>
            /// 结束时间
            /// </summary>
            public DateTime? EndTime { get; set; }

            /// <summary>
            /// 排序字段
            /// </summary>
            [Query(QueryOperator.排序, orderDirection: OrderDirection.降序, orderPriority: 1)]
            public DateTime? CreateTime { get; set; }
        }

        /// <summary>
        /// 根据OpenID获取用户
        /// </summary>
        /// <param name="openId">微信OpenID</param>
        /// <returns>用户实体</returns>
        public async Task<User?> GetByOpenIdAsync(string openId)
        {
            return await _context.Set<User>()
                .FirstOrDefaultAsync(u => u.OpenId == openId);
        }

        /// <summary>
        /// 检查OpenID是否存在
        /// </summary>
        /// <param name="openId">微信OpenID</param>
        /// <param name="excludeId">排除的用户ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsOpenIdAsync(string openId, int? excludeId = null)
        {
            var query = _context.Set<User>().Where(u => u.OpenId == openId);
            if (excludeId.HasValue)
            {
                query = query.Where(u => u.Id != excludeId.Value);
            }
            return await query.AnyAsync();
        }

        /// <summary>
        /// 根据员工ID获取绑定的用户列表
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <returns>用户列表</returns>
        public async Task<List<User>> GetByEmployeeIdAsync(string employeeId)
        {
            return await _context.Set<User>()
                .Where(u => u.EmployeeId == employeeId)
                .OrderBy(u => u.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 更新用户最后登录时间
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateLastLoginAsync(string userId)
        {
            var user = await _context.Set<User>().FindAsync(userId);
            if (user == null) return false;

            user.LastLogin = DateTime.Now;
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 批量转移用户到新员工
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="newEmployeeId">新员工ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> BatchTransferAsync(List<string> userIds, string newEmployeeId)
        {
            var users = await _context.Set<User>()
                .Where(u => userIds.Contains(u.Id))
                .ToListAsync();

            foreach (var user in users)
            {
                user.EmployeeId = newEmployeeId;
                user.UpdateTime = DateTime.Now;
            }

            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户实体</returns>
        public async Task<User?> GetByIdAsync(int id)
        {
            return await _context.Set<User>().FindAsync(id);
        }

        /// <summary>
        /// 根据ID获取用户（支持字符串ID）
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户实体</returns>
        public async Task<User?> GetByIdAsync(string id)
        {
            // 尝试解析为整数ID
            if (int.TryParse(id, out int intId))
            {
                return await GetByIdAsync(intId);
            }

            // 如果不是整数，则可能是IP用户或其他字符串ID
            // 对于IP用户，我们创建一个虚拟的User对象用于显示
            if (id.StartsWith("ip_") || (id.Length == 32 && id.All(c => char.IsLetterOrDigit(c))))
            {
                return new User
                {
                    Id = Guid.NewGuid().ToString(), // 虚拟ID
                    Nickname = $"访客_{id.Substring(0, Math.Min(8, id.Length))}",
                    OpenId = null,
                    UnionId = null,
                    Avatar = null,
                    EmployeeId = null,
                    CreateTime = DateTime.Now,
                    LastLogin = DateTime.Now
                };
            }

            return null; // 其他情况返回null
        }

        /// <summary>
        /// 获取分页用户列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PageEntity<User>> GetPagedListAsync(Queryable queryable)
        {
            return await GetPageDataAsync(queryable, q => q.OrderByDescending(x => x.CreateTime));
        }

        /// <summary>
        /// 根据用户ID列表获取用户
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>用户列表</returns>
        public async Task<List<User>> GetByUserIdsAsync(List<string> userIds)
        {
            return await _context.Set<User>()
                .Where(u => userIds.Contains(u.Id))
                .ToListAsync();
        }

        /// <summary>
        /// 根据用户ID列表获取用户（别名方法，用于统计服务）
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>用户列表</returns>
        public async Task<List<User>> GetUsersByIdsAsync(List<string> userIds)
        {
            return await GetByUserIdsAsync(userIds);
        }

        /// <summary>
        /// 根据员工ID获取用户列表（用于统计服务）
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <returns>用户列表</returns>
        public async Task<List<User>> GetUsersByEmployeeIdAsync(int employeeId)
        {
            return await _context.Set<User>()
                .Where(u => u.EmployeeId == employeeId.ToString())
                .OrderBy(u => u.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取用户总数
        /// </summary>
        /// <returns>用户总数</returns>
        public async Task<int> GetCountAsync()
        {
            return await _context.Set<User>().CountAsync();
        }

        /// <summary>
        /// 获取指定时间范围内的用户数量
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>用户数量</returns>
        public async Task<int> GetUserCountByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Set<User>()
                .Where(u => u.CreateTime >= startDate && u.CreateTime <= endDate)
                .CountAsync();
        }

        /// <summary>
        /// 获取用户查询对象
        /// </summary>
        /// <returns>查询对象</returns>
        public IQueryable<User> GetQueryable()
        => _context.Set<User>().AsQueryable();

        /// <summary>
        /// 更新用户绑定的员工
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="employeeId">员工ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateUserEmployeeAsync(int userId, string? employeeId)
        {
            var user = await _context.Set<User>().FindAsync(userId);
            if (user == null) return false;

            user.EmployeeId = employeeId;
            user.UpdateTime = DateTime.Now;
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 根据用户范围分页查询用户列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <param name="accessibleUserIds">可访问的用户ID列表</param>
        /// <returns>分页结果</returns>
        public async Task<PageEntity<User>> GetPagedListWithScopeAsync(Queryable queryable, List<string> accessibleUserIds)
        {
            // 如果没有可访问的用户，直接返回空结果
            if (accessibleUserIds.Count == 0)
            {
                return new PageEntity<User>
                {
                    List = [],
                    TotalCount = 0,
                    PageIndex = queryable.PageIndex,
                    PageSize = queryable.PageSize
                };
            }

            // 构建基础查询
            var query = _context.Set<User>().AsQueryable();

            // 添加用户范围限制
            query = query.Where(u => accessibleUserIds.Contains(u.Id));

            // 应用其他查询条件
            if (!string.IsNullOrEmpty(queryable.Nickname))
                query = query.Where(u => u.Nickname != null && u.Nickname.Contains(queryable.Nickname));

            if (!string.IsNullOrEmpty(queryable.EmployeeId))
                query = query.Where(u => u.EmployeeId == queryable.EmployeeId);

            if (queryable.StartTime.HasValue)
                query = query.Where(u => u.CreateTime >= queryable.StartTime.Value);

            if (queryable.EndTime.HasValue)
                query = query.Where(u => u.CreateTime <= queryable.EndTime.Value);

            // 排序
            query = query.OrderByDescending(u => u.CreateTime);

            // 分页
            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((queryable.PageIndex - 1) * queryable.PageSize)
                .Take(queryable.PageSize)
                .ToListAsync();

            return new PageEntity<User>
            {
                List = items,
                TotalCount = totalCount,
                PageIndex = queryable.PageIndex,
                PageSize = queryable.PageSize
            };
        }
    }
}
