using BLL.VideoService;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 批次管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class BatchController(BatchService batchService) : BaseController
    {
        private readonly BatchService _batchService = batchService;

        /// <summary>
        /// 添加批次
        /// </summary>
        /// <param name="createDto">创建批次DTO</param>
        /// <returns>批次ID</returns>
        [HttpPost(Name = "Batch_Add")]
        public async Task<Result<int>> Add([FromBody] BatchCreateDto createDto)
        {
            var batchId = await _batchService.AddBatchAsync(createDto, GetCurrentUserInfo());
            return Success(batchId, "批次添加成功");
        }

        /// <summary>
        /// 删除批次
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否成功</returns>
        [HttpDelete("{batchId}", Name = "Batch_Delete")]
        public async Task<Result<bool>> Delete(int batchId)
        {
            var result = await _batchService.DeleteBatchAsync(batchId, GetCurrentUserInfo());
            return Success(result, "批次删除成功");
        }

        /// <summary>
        /// 获取批次详情
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <returns>批次详情</returns>
        [HttpGet("{batchId}", Name = "Batch_Get")]
        public async Task<Result<BatchResponseDto?>> Get(int batchId)
        {
            var batch = await _batchService.GetBatchAsync(batchId);
            return Success(batch);
        }

        /// <summary>
        /// 分页查询批次列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>批次列表</returns>
        [HttpGet(Name = "Batch_GetList")]
        public async Task<Result<PagedResult<BatchResponseDto>>> GetList([FromQuery] BatchQueryDto queryDto)
        {
            var result = await _batchService.GetBatchPagedListAsync(queryDto);
            return Success(result);
        }

        /// <summary>
        /// 获取批次统计信息
        /// 支持基于用户权限的数据过滤：
        /// - 超级管理员：可查看所有用户数据
        /// - 管理员：可查看所有员工及其用户的数据
        /// - 员工：只能查看自己绑定用户的数据
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <returns>批次统计信息</returns>
        [HttpGet("{batchId}/statistics", Name = "Batch_GetStatistics")]
        public async Task<Result<BatchStatisticsDto>> GetBatchStatistics(int batchId)
        {
            var currentJwtUserInfo = GetCurrentJwtUserInfo();
            var statistics = await _batchService.GetBatchStatisticsAsync(batchId, currentJwtUserInfo);
            return Success(statistics, "获取批次统计成功");
        }
    }
}
