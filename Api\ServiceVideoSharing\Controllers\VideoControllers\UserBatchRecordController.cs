using BLL.VideoService;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 用户批次记录控制器
    /// 提供观看、答题、红包一体化的API接口
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class UserBatchRecordController(UserBatchRecordService userBatchRecordService) : BaseController
    {
        private readonly UserBatchRecordService _userBatchRecordService = userBatchRecordService;

        #region 记录管理

        /// <summary>
        /// 创建或获取用户批次记录
        /// 用户进入视频页面时调用，确保有记录存在
        /// </summary>
        /// <param name="createDto">创建记录DTO</param>
        /// <returns>用户批次记录</returns>
        [HttpPost("create-or-get", Name = "UserBatchRecord_CreateOrGet")]
        public async Task<Result<UserBatchRecordResponseDto>> CreateOrGetRecord([FromBody] UserBatchRecordCreateDto createDto)
        {
            var record = await _userBatchRecordService.CreateOrGetRecordAsync(createDto, GetCurrentUserInfo());
            return Success(record, "记录获取成功");
        }

        /// <summary>
        /// 获取用户批次记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>用户批次记录</returns>
        [HttpGet("{userId}/{batchId}", Name = "UserBatchRecord_Get")]
        public async Task<Result<UserBatchRecordResponseDto?>> GetRecord(string userId, int batchId)
        {
            var record = await _userBatchRecordService.GetRecordAsync(userId, batchId);
            return Success(record, "记录获取成功");
        }

        /// <summary>
        /// 获取用户的所有批次记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户批次记录列表</returns>
        [HttpGet("user/{userId}", Name = "UserBatchRecord_GetUserRecords")]
        public async Task<Result<List<UserBatchRecordSummaryDto>>> GetUserRecords(string userId)
        {
            var records = await _userBatchRecordService.GetUserRecordsAsync(userId);
            return Success(records, "用户记录获取成功");
        }

        #endregion

        #region 观看进度管理

        /// <summary>
        /// 更新观看进度
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="updateDto">观看进度更新DTO</param>
        /// <returns>是否成功</returns>
        [HttpPost("{userId}/watch-progress", Name = "UserBatchRecord_UpdateWatchProgress")]
        public async Task<Result<bool>> UpdateWatchProgress(string userId, [FromBody] WatchProgressUpdateDto updateDto)
        {
            var result = await _userBatchRecordService.UpdateWatchProgressAsync(userId, updateDto, GetCurrentUserInfo());
            return Success(result, "观看进度更新成功");
        }

        /// <summary>
        /// 检查用户是否已观看
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否已观看</returns>
        [HttpGet("{userId}/{batchId}/has-watched", Name = "UserBatchRecord_HasWatched")]
        public async Task<Result<bool>> HasWatched(string userId, int batchId)
        {
            var result = await _userBatchRecordService.HasWatchedAsync(userId, batchId);
            return Success(result, "查询成功");
        }

        /// <summary>
        /// 检查用户是否完播
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否完播</returns>
        [HttpGet("{userId}/{batchId}/has-completed", Name = "UserBatchRecord_HasCompleted")]
        public async Task<Result<bool>> HasCompleted(string userId, int batchId)
        {
            var result = await _userBatchRecordService.HasCompletedAsync(userId, batchId);
            return Success(result, "查询成功");
        }

        /// <summary>
        /// 获取用户观看状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>观看状态信息</returns>
        [HttpGet("{userId}/{batchId}/watch-status", Name = "UserBatchRecord_GetWatchStatus")]
        public async Task<Result<WatchStatusDto>> GetWatchStatus(string userId, int batchId)
        {
            var status = await _userBatchRecordService.GetWatchStatusAsync(userId, batchId);
            return Success(status, "观看状态获取成功");
        }

        /// <summary>
        /// 开始观看（记录开始时间）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否成功</returns>
        [HttpPost("{userId}/{batchId}/start-watching", Name = "UserBatchRecord_StartWatching")]
        public async Task<Result<bool>> StartWatching(string userId, int batchId)
        {
            var result = await _userBatchRecordService.StartWatchingAsync(userId, batchId, GetCurrentUserInfo());
            return Success(result, "开始观看记录成功");
        }

        /// <summary>
        /// 批量更新观看进度
        /// 用于前端定时同步多个用户的观看进度
        /// </summary>
        /// <param name="progressUpdates">进度更新列表</param>
        /// <returns>成功更新的数量</returns>
        [HttpPost("batch-update-progress", Name = "UserBatchRecord_BatchUpdateProgress")]
        public async Task<Result<int>> BatchUpdateProgress([FromBody] List<BatchWatchProgressUpdateDto> progressUpdates)
        {
            var successCount = await _userBatchRecordService.BatchUpdateWatchProgressAsync(progressUpdates, GetCurrentUserInfo());
            return Success(successCount, $"批量更新完成，成功更新 {successCount} 条记录");
        }

        #endregion

        #region 答题管理

        /// <summary>
        /// 提交答题结果
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="answerDto">答题结果DTO</param>
        /// <returns>是否成功</returns>
        [HttpPost("{userId}/submit-answer", Name = "UserBatchRecord_SubmitAnswer")]
        public async Task<Result<bool>> SubmitAnswer(string userId, [FromBody] AnswerSubmitDto answerDto)
        {
            var result = await _userBatchRecordService.SubmitAnswerAsync(userId, answerDto, GetCurrentUserInfo());
            return Success(result, "答题结果提交成功");
        }

        /// <summary>
        /// 检查用户是否已答题
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否已答题</returns>
        [HttpGet("{userId}/{batchId}/has-answered", Name = "UserBatchRecord_HasAnswered")]
        public async Task<Result<bool>> HasAnswered(string userId, int batchId)
        {
            var result = await _userBatchRecordService.HasAnsweredAsync(userId, batchId);
            return Success(result, "查询成功");
        }

        /// <summary>
        /// 获取用户答题状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>答题状态信息</returns>
        [HttpGet("{userId}/{batchId}/answer-status", Name = "UserBatchRecord_GetAnswerStatus")]
        public async Task<Result<AnswerStatusDto>> GetAnswerStatus(string userId, int batchId)
        {
            var status = await _userBatchRecordService.GetAnswerStatusAsync(userId, batchId);
            return Success(status, "答题状态获取成功");
        }

        /// <summary>
        /// 获取用户答题详情
        /// 需要权限验证：管理员可查看所有，员工只能查看自己绑定的用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>答题详情</returns>
        [HttpGet("{userId}/{batchId}/answer-detail", Name = "UserBatchRecord_GetAnswerDetail")]
        public async Task<Result<AnswerDetailDto?>> GetAnswerDetail(string userId, int batchId)
        {
            var currentJwtUserInfo = GetCurrentJwtUserInfo();
            var detail = await _userBatchRecordService.GetAnswerDetailAsync(userId, batchId, currentJwtUserInfo);
            return Success(detail, "答题详情获取成功");
        }

        /// <summary>
        /// 验证答题资格
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>验证结果</returns>
        [HttpGet("{userId}/{batchId}/answer-eligibility", Name = "UserBatchRecord_CheckAnswerEligibility")]
        public async Task<Result<AnswerEligibilityDto>> CheckAnswerEligibility(string userId, int batchId)
        {
            var eligibility = await _userBatchRecordService.CheckAnswerEligibilityAsync(userId, batchId);
            return Success(eligibility, "答题资格验证完成");
        }

        #endregion

        #region 红包管理

        /// <summary>
        /// 发放红包
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="rewardDto">红包发放DTO</param>
        /// <returns>是否成功</returns>
        [HttpPost("{userId}/grant-reward", Name = "UserBatchRecord_GrantReward")]
        public async Task<Result<bool>> GrantReward(string userId, [FromBody] RewardGrantDto rewardDto)
        {
            var result = await _userBatchRecordService.GrantRewardAsync(userId, rewardDto, GetCurrentUserInfo());
            return Success(result, "红包发放成功");
        }

        /// <summary>
        /// 更新红包状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="statusDto">红包状态更新DTO</param>
        /// <returns>是否成功</returns>
        [HttpPost("{userId}/update-reward-status", Name = "UserBatchRecord_UpdateRewardStatus")]
        public async Task<Result<bool>> UpdateRewardStatus(string userId, [FromBody] RewardStatusUpdateDto statusDto)
        {
            var result = await _userBatchRecordService.UpdateRewardStatusAsync(userId, statusDto, GetCurrentUserInfo());
            return Success(result, "红包状态更新成功");
        }

        /// <summary>
        /// 检查用户是否已获得红包
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否已获得红包</returns>
        [HttpGet("{userId}/{batchId}/has-reward", Name = "UserBatchRecord_HasReward")]
        public async Task<Result<bool>> HasReward(string userId, int batchId)
        {
            var result = await _userBatchRecordService.HasRewardAsync(userId, batchId);
            return Success(result, "查询成功");
        }

        /// <summary>
        /// 获取用户红包状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>红包状态信息</returns>
        [HttpGet("{userId}/{batchId}/reward-status", Name = "UserBatchRecord_GetRewardStatus")]
        public async Task<Result<RewardStatusDto>> GetRewardStatus(string userId, int batchId)
        {
            var status = await _userBatchRecordService.GetRewardStatusAsync(userId, batchId);
            return Success(status, "红包状态获取成功");
        }

        /// <summary>
        /// 验证红包发放资格
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>验证结果</returns>
        [HttpGet("{userId}/{batchId}/reward-eligibility", Name = "UserBatchRecord_CheckRewardEligibility")]
        public async Task<Result<RewardEligibilityDto>> CheckRewardEligibility(string userId, int batchId)
        {
            var eligibility = await _userBatchRecordService.CheckRewardEligibilityAsync(userId, batchId);
            return Success(eligibility, "红包发放资格验证完成");
        }

        /// <summary>
        /// 批量发放红包
        /// 为指定批次中符合条件的所有用户发放红包
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="rewardAmount">红包金额</param>
        /// <returns>发放结果</returns>
        [HttpPost("batch/{batchId}/grant-rewards", Name = "UserBatchRecord_BatchGrantRewards")]
        public async Task<Result<BatchRewardResultDto>> BatchGrantRewards(int batchId, [FromBody] decimal rewardAmount)
        {
            var result = await _userBatchRecordService.BatchGrantRewardAsync(batchId, rewardAmount, GetCurrentUserInfo());
            return Success(result, "批量红包发放完成");
        }

        #endregion

        #region 统计和查询

        /// <summary>
        /// 获取批次统计数据
        /// 支持基于用户权限的数据过滤：
        /// - 超级管理员：可查看所有用户数据
        /// - 管理员：可查看所有员工及其用户的数据
        /// - 员工：只能查看自己绑定用户的数据
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <returns>批次统计数据</returns>
        [HttpGet("batch/{batchId}/statistics", Name = "UserBatchRecord_GetBatchStatistics")]
        public async Task<Result<BatchStatisticsFromRecordDto>> GetBatchStatistics(int batchId)
        {
            var currentJwtUserInfo = GetCurrentJwtUserInfo();
            var statistics = await _userBatchRecordService.GetBatchStatisticsAsync(batchId, currentJwtUserInfo);
            return Success(statistics, "批次统计获取成功");
        }

        /// <summary>
        /// 获取批次记录列表
        /// 支持基于用户权限的数据过滤
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <returns>批次记录列表</returns>
        [HttpGet("batch/{batchId}/records", Name = "UserBatchRecord_GetBatchRecords")]
        public async Task<Result<List<UserBatchRecordResponseDto>>> GetBatchRecords(int batchId)
        {
            var currentJwtUserInfo = GetCurrentJwtUserInfo();
            var records = await _userBatchRecordService.GetBatchRecordsAsync(batchId, currentJwtUserInfo);
            return Success(records, "批次记录获取成功");
        }

        #endregion

        #region 兼容性接口（保持与旧API的兼容性）

        /// <summary>
        /// 兼容旧的ViewRecord创建接口
        /// </summary>
        [HttpPost("~/api/ViewRecord", Name = "ViewRecord_Create_Compat")]
        public async Task<Result<int>> CreateViewRecordCompat([FromBody] UserBatchRecordCreateDto createDto)
        {
            var record = await _userBatchRecordService.CreateOrGetRecordAsync(createDto, GetCurrentUserInfo());
            return Success(record.Id, "观看记录创建成功");
        }

        /// <summary>
        /// 兼容旧的ViewRecord进度更新接口
        /// </summary>
        [HttpPost("~/api/ViewRecord/update-progress", Name = "ViewRecord_UpdateProgress_Compat")]
        public async Task<Result<bool>> UpdateViewProgressCompat([FromBody] WatchProgressUpdateDto updateDto)
        {
            var currentUser = GetCurrentUserInfo();
            var userId = int.Parse(currentUser.UserId);
            var result = await _userBatchRecordService.UpdateWatchProgressAsync(userId, updateDto, currentUser);
            return Success(result, "观看进度更新成功");
        }

        /// <summary>
        /// 兼容旧的ViewRecord批次记录查询接口
        /// </summary>
        [HttpGet("~/api/ViewRecord/batch/{batchId}", Name = "ViewRecord_GetBatchRecords_Compat")]
        public async Task<Result<PagedResult<UserBatchRecordResponseDto>>> GetBatchViewRecordsCompat(int batchId, [FromQuery] int pageIndex = 1, [FromQuery] int pageSize = 20)
        {
            var currentJwtUserInfo = GetCurrentJwtUserInfo();
            var records = await _userBatchRecordService.GetBatchRecordsAsync(batchId, currentJwtUserInfo);

            // 分页处理
            var pagedRecords = records.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
            var result = new PagedResult<UserBatchRecordResponseDto>
            {
                Items = pagedRecords,
                TotalCount = records.Count,
                PageIndex = pageIndex,
                PageSize = pageSize
                // TotalPages 是只读属性，会自动计算
            };

            return Success(result, "批次观看记录获取成功");
        }

        /// <summary>
        /// 兼容旧的ViewRecord统计接口
        /// </summary>
        [HttpGet("~/api/ViewRecord/statistics", Name = "ViewRecord_GetStatistics_Compat")]
        public async Task<Result<BatchStatisticsFromRecordDto>> GetViewStatisticsCompat([FromQuery] int? batchId = null)
        {
            if (batchId.HasValue)
            {
                var currentJwtUserInfo = GetCurrentJwtUserInfo();
                var statistics = await _userBatchRecordService.GetBatchStatisticsAsync(batchId.Value, currentJwtUserInfo);
                return Success(statistics, "观看统计获取成功");
            }

            // 如果没有指定批次ID，返回空统计
            var emptyStats = new BatchStatisticsFromRecordDto();
            return Success(emptyStats, "观看统计获取成功");
        }

        /// <summary>
        /// 兼容旧的AnswerRecord提交接口
        /// </summary>
        [HttpPost("~/api/AnswerRecord/submit", Name = "AnswerRecord_Submit_Compat")]
        public async Task<Result<int>> SubmitAnswerRecordCompat([FromBody] AnswerSubmitDto answerDto)
        {
            var currentUser = GetCurrentUserInfo();
            var userId = int.Parse(currentUser.UserId);
            var result = await _userBatchRecordService.SubmitAnswerAsync(userId, answerDto, currentUser);
            return Success(result ? 1 : 0, "答题提交成功");
        }

        /// <summary>
        /// 兼容旧的AnswerRecord统计接口
        /// </summary>
        [HttpGet("~/api/AnswerRecord/statistics/{batchId}", Name = "AnswerRecord_GetStatistics_Compat")]
        public async Task<Result<BatchStatisticsFromRecordDto>> GetAnswerStatisticsCompat(int batchId)
        {
            var currentJwtUserInfo = GetCurrentJwtUserInfo();
            var statistics = await _userBatchRecordService.GetBatchStatisticsAsync(batchId, currentJwtUserInfo);
            return Success(statistics, "答题统计获取成功");
        }

        /// <summary>
        /// 兼容旧的Reward用户奖励查询接口
        /// </summary>
        [HttpGet("~/api/Reward/user/{userId}", Name = "Reward_GetUserRewards_Compat")]
        public async Task<Result<List<UserBatchRecordResponseDto>>> GetUserRewardsCompat(string userId)
        {
            var records = await _userBatchRecordService.GetUserRecordsAsync(userId);
            var rewardRecords = records.Where(r => r.RewardAmount > 0).ToList();

            // 转换为UserBatchRecordResponseDto
            var responseRecords = rewardRecords.Select(r => new UserBatchRecordResponseDto
            {
                Id = r.Id,
                UserId = r.UserId,
                BatchId = r.BatchId,
                IsCompleted = r.IsCompleted,
                RewardAmount = r.RewardAmount,
                CreateTime = r.CreateTime
                // 其他属性根据需要填充
            }).ToList();

            return Success(responseRecords, "用户奖励记录获取成功");
        }

        #endregion
    }
}
